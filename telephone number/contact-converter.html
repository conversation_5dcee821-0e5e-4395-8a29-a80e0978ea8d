<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人表格转VCF工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .step {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .step.active {
            border-color: #4facfe;
            background: #f8fbff;
        }

        .step-title {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .step-number {
            background: #4facfe;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }

        .file-upload {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }

        .file-upload input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .file-upload-label {
            display: block;
            padding: 20px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-upload-label:hover {
            background: #e9ecef;
            border-color: #4facfe;
        }

        .file-upload-label.dragover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .upload-icon {
            font-size: 3em;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .data-preview {
            margin-top: 20px;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            overflow: hidden;
        }

        .table-container {
            max-height: 400px;
            overflow: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .column-selector {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .selector-group {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }

        .selector-item {
            flex: 1;
            min-width: 200px;
        }

        .selector-item label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .selector-item select {
            width: 100%;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-size: 1em;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            width: 0%;
            transition: width 0.3s ease;
        }

        .result-info {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }

        .error-info {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 20px;
            }
            
            .selector-group {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 联系人表格转VCF工具</h1>
            <p>轻松将Excel/CSV表格中的联系人信息转换为手机通讯录可导入的VCF文件</p>
        </div>

        <div class="main-content">
            <!-- 步骤1: 文件导入 -->
            <div class="step active" id="step1">
                <div class="step-title">
                    <div class="step-number">1</div>
                    导入表格文件
                </div>
                <div class="file-upload">
                    <input type="file" id="fileInput" accept=".xlsx,.xls,.csv" />
                    <label for="fileInput" class="file-upload-label" id="fileLabel">
                        <div class="upload-icon">📁</div>
                        <div>点击选择文件或拖拽文件到此处</div>
                        <div style="font-size: 0.9em; color: #6c757d; margin-top: 10px;">
                            支持 Excel (.xlsx, .xls) 和 CSV 文件
                        </div>
                    </label>
                </div>
                <div id="fileInfo" class="hidden"></div>
            </div>

            <!-- 步骤2: 数据预览和列选择 -->
            <div class="step" id="step2">
                <div class="step-title">
                    <div class="step-number">2</div>
                    数据预览和列选择
                </div>
                <div id="dataPreview" class="hidden">
                    <div class="column-selector">
                        <div class="selector-group">
                            <div class="selector-item">
                                <label for="nameColumn">姓名列:</label>
                                <select id="nameColumn">
                                    <option value="">请选择姓名列</option>
                                </select>
                            </div>
                            <div class="selector-item">
                                <label for="phoneColumn">电话号码列:</label>
                                <select id="phoneColumn">
                                    <option value="">请选择电话号码列</option>
                                </select>
                            </div>
                            <div class="selector-item">
                                <button onclick="autoDetectColumns()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px;">
                                    🔍 重新自动识别
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="data-preview">
                        <div class="table-container">
                            <table id="previewTable">
                                <thead id="tableHead"></thead>
                                <tbody id="tableBody"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 步骤3: 生成VCF文件 -->
            <div class="step" id="step3">
                <div class="step-title">
                    <div class="step-number">3</div>
                    生成VCF文件
                </div>
                <button class="btn" id="generateBtn" disabled onclick="generateVCF()">
                    🚀 生成VCF文件
                </button>
                <div class="progress-bar hidden" id="progressBar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div id="resultInfo" class="hidden"></div>
            </div>
        </div>
    </div>

    <script>
        let tableData = [];
        let headers = [];

        // 文件上传处理
        const fileInput = document.getElementById('fileInput');
        const fileLabel = document.getElementById('fileLabel');
        
        fileInput.addEventListener('change', handleFile);
        
        // 拖拽上传
        fileLabel.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileLabel.classList.add('dragover');
        });
        
        fileLabel.addEventListener('dragleave', () => {
            fileLabel.classList.remove('dragover');
        });
        
        fileLabel.addEventListener('drop', (e) => {
            e.preventDefault();
            fileLabel.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFile();
            }
        });

        function handleFile() {
            const file = fileInput.files[0];
            if (!file) return;

            const fileInfo = document.getElementById('fileInfo');
            fileInfo.innerHTML = `
                <div class="result-info">
                    <strong>已选择文件:</strong> ${file.name} (${(file.size / 1024).toFixed(1)} KB)
                </div>
            `;
            fileInfo.classList.remove('hidden');

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    if (file.name.toLowerCase().endsWith('.csv')) {
                        parseCSV(e.target.result);
                    } else {
                        parseExcel(e.target.result);
                    }
                } catch (error) {
                    showError('文件解析失败: ' + error.message);
                }
            };

            if (file.name.toLowerCase().endsWith('.csv')) {
                reader.readAsText(file, 'UTF-8');
            } else {
                reader.readAsArrayBuffer(file);
            }
        }

        function parseCSV(csvText) {
            const lines = csvText.split('\n').filter(line => line.trim());
            if (lines.length === 0) {
                showError('CSV文件为空');
                return;
            }

            headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
            console.log('CSV表头:', headers);

            tableData = [];

            for (let i = 1; i < lines.length; i++) {
                const row = lines[i].split(',').map(cell => String(cell || '').trim().replace(/"/g, ''));

                // 确保行有足够的列
                const paddedRow = [];
                for (let j = 0; j < headers.length; j++) {
                    paddedRow[j] = row[j] || '';
                }

                if (paddedRow.some(cell => cell && cell !== '')) { // 过滤完全空的行
                    tableData.push(paddedRow);
                }
            }

            console.log('CSV处理后的数据行数:', tableData.length);
            console.log('前5行数据:', tableData.slice(0, 5));

            displayPreview();
        }

        function parseExcel(arrayBuffer) {
            const workbook = XLSX.read(arrayBuffer, { type: 'array' });
            const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

            // 使用更详细的选项来解析Excel
            const jsonData = XLSX.utils.sheet_to_json(firstSheet, {
                header: 1,
                defval: '', // 空单元格默认值
                raw: false, // 不使用原始值，确保数字被转换为字符串
                dateNF: 'YYYY-MM-DD' // 日期格式
            });

            console.log('Excel解析结果:', jsonData);
            console.log('总行数:', jsonData.length);

            if (jsonData.length === 0) {
                showError('Excel文件为空');
                return;
            }

            // 处理表头
            headers = jsonData[0].map(h => String(h || '').trim());
            console.log('表头:', headers);

            // 处理数据行 - 更宽松的过滤条件
            tableData = [];
            for (let i = 1; i < jsonData.length; i++) {
                const row = jsonData[i];
                // 确保每行都有足够的列
                const paddedRow = [];
                for (let j = 0; j < headers.length; j++) {
                    // 将所有值转换为字符串，包括数字
                    const cellValue = row[j];
                    if (cellValue !== undefined && cellValue !== null) {
                        paddedRow[j] = String(cellValue).trim();
                    } else {
                        paddedRow[j] = '';
                    }
                }

                // 只要行中有任何非空内容就保留
                if (paddedRow.some(cell => cell && cell !== '')) {
                    tableData.push(paddedRow);
                }
            }

            console.log('处理后的数据行数:', tableData.length);
            console.log('前5行数据:', tableData.slice(0, 5));

            displayPreview();
        }

        function displayPreview() {
            // 显示步骤2
            document.getElementById('step2').classList.add('active');
            document.getElementById('dataPreview').classList.remove('hidden');

            // 填充列选择器
            const nameSelect = document.getElementById('nameColumn');
            const phoneSelect = document.getElementById('phoneColumn');
            
            nameSelect.innerHTML = '<option value="">请选择姓名列</option>';
            phoneSelect.innerHTML = '<option value="">请选择电话号码列</option>';

            headers.forEach((header, index) => {
                const option1 = new Option(header, index);
                const option2 = new Option(header, index);
                nameSelect.add(option1);
                phoneSelect.add(option2);
            });

            // 自动识别列
            autoDetectColumns();

            // 显示预览表格
            displayTable();

            // 监听列选择变化
            nameSelect.addEventListener('change', checkSelections);
            phoneSelect.addEventListener('change', checkSelections);
        }

        function autoDetectColumns() {
            const nameSelect = document.getElementById('nameColumn');
            const phoneSelect = document.getElementById('phoneColumn');

            console.log('开始自动识别列...');
            console.log('表头:', headers);
            console.log('前5行数据样本:', tableData.slice(0, 5));

            let nameColumnIndex = -1;
            let phoneColumnIndex = -1;

            // 通过数据内容识别列，而不仅仅是表头
            for (let colIndex = 0; colIndex < headers.length; colIndex++) {
                const header = headers[colIndex];
                console.log(`检查列 ${colIndex}: "${header}"`);

                // 获取该列的前10个非空值进行分析
                const columnSamples = [];
                for (let rowIndex = 0; rowIndex < Math.min(tableData.length, 10); rowIndex++) {
                    const cellValue = String(tableData[rowIndex][colIndex] || '').trim();
                    if (cellValue) {
                        columnSamples.push(cellValue);
                    }
                }

                console.log(`列 ${colIndex} 样本数据:`, columnSamples);

                // 识别手机号码列：11位数字
                const phonePattern = /^1[3-9]\d{9}$/; // 中国手机号码格式
                const phoneMatches = columnSamples.filter(sample => phonePattern.test(sample));
                const phoneRatio = columnSamples.length > 0 ? phoneMatches.length / columnSamples.length : 0;

                console.log(`列 ${colIndex} 手机号码匹配:`, phoneMatches.length, '/', columnSamples.length, '=', phoneRatio);

                if (phoneRatio > 0.8 && phoneColumnIndex === -1) { // 80%以上匹配手机号码格式
                    phoneColumnIndex = colIndex;
                    console.log(`识别为手机号码列: ${colIndex}`);
                }

                // 识别姓名列：2-4个中文字符
                const namePattern = /^[\u4e00-\u9fa5]{2,4}$/; // 2-4个中文字符
                const nameMatches = columnSamples.filter(sample => namePattern.test(sample));
                const nameRatio = columnSamples.length > 0 ? nameMatches.length / columnSamples.length : 0;

                console.log(`列 ${colIndex} 姓名匹配:`, nameMatches.length, '/', columnSamples.length, '=', nameRatio);

                if (nameRatio > 0.8 && nameColumnIndex === -1) { // 80%以上匹配姓名格式
                    nameColumnIndex = colIndex;
                    console.log(`识别为姓名列: ${colIndex}`);
                }
            }

            // 设置识别结果
            if (nameColumnIndex !== -1) {
                nameSelect.value = nameColumnIndex;
                console.log(`自动选择姓名列: ${nameColumnIndex} (${headers[nameColumnIndex]})`);
            }

            if (phoneColumnIndex !== -1) {
                phoneSelect.value = phoneColumnIndex;
                console.log(`自动选择手机号码列: ${phoneColumnIndex} (${headers[phoneColumnIndex]})`);
            }

            // 如果没有自动识别成功，尝试通过表头识别
            if (nameColumnIndex === -1) {
                headers.forEach((header, index) => {
                    const lowerHeader = header.toLowerCase();
                    if (lowerHeader.includes('姓名') || lowerHeader.includes('名字') ||
                        lowerHeader.includes('name') || lowerHeader.includes('联系人')) {
                        nameSelect.value = index;
                        console.log(`通过表头识别姓名列: ${index}`);
                    }
                });
            }

            if (phoneColumnIndex === -1) {
                headers.forEach((header, index) => {
                    const lowerHeader = header.toLowerCase();
                    if (lowerHeader.includes('电话') || lowerHeader.includes('手机') ||
                        lowerHeader.includes('phone') || lowerHeader.includes('tel') ||
                        lowerHeader.includes('mobile') || lowerHeader.includes('号码')) {
                        phoneSelect.value = index;
                        console.log(`通过表头识别电话列: ${index}`);
                    }
                });
            }

            checkSelections();
        }

        function displayTable() {
            updatePreviewTable();
        }

        function updatePreviewTable() {
            const tableHead = document.getElementById('tableHead');
            const tableBody = document.getElementById('tableBody');
            const nameColumnIndex = parseInt(document.getElementById('nameColumn').value);
            const phoneColumnIndex = parseInt(document.getElementById('phoneColumn').value);

            console.log('更新预览表格:', {
                totalRows: tableData.length,
                nameColumnIndex,
                phoneColumnIndex,
                headers
            });

            if (isNaN(nameColumnIndex) || isNaN(phoneColumnIndex)) {
                // 显示所有列
                tableHead.innerHTML = '<tr>' + headers.map(h => `<th>${h}</th>`).join('') + '</tr>';
                const displayRows = tableData.slice(0, 20);
                tableBody.innerHTML = displayRows.map((row, index) =>
                    '<tr>' + row.map(cell => `<td>${cell || '<span style="color: #ccc;">空</span>'}</td>`).join('') + '</tr>'
                ).join('');

                // 显示基本统计
                const statsInfo = document.getElementById('statsInfo') || createStatsInfo();
                statsInfo.innerHTML = `
                    <div class="result-info">
                        <strong>📊 文件统计:</strong><br>
                        总计: ${tableData.length} 行数据<br>
                        预览: 显示前20行 (请选择姓名和电话列查看完整统计)
                    </div>
                `;
            } else {
                // 只显示选中的姓名和电话号码列
                const nameHeader = headers[nameColumnIndex];
                const phoneHeader = headers[phoneColumnIndex];

                console.log('选中的列:', { nameHeader, phoneHeader });

                tableHead.innerHTML = `
                    <tr>
                        <th>序号</th>
                        <th>${nameHeader} (姓名)</th>
                        <th>${phoneHeader} (电话)</th>
                        <th>状态</th>
                    </tr>
                `;

                // 显示所有有效数据，不限制为20行
                let validRows = [];
                let rowNumber = 1;
                let debugCount = 0;

                tableData.forEach((row, index) => {
                    const name = String(row[nameColumnIndex] || '').trim();
                    const phone = String(row[phoneColumnIndex] || '').trim();

                    // 调试前10行
                    if (debugCount < 10) {
                        console.log(`行 ${index + 1}:`, { name, phone, row });
                        debugCount++;
                    }

                    if (name || phone) {  // 只要有姓名或电话就显示
                        const status = (name && phone) ?
                            '<span style="color: green;">✓ 有效</span>' :
                            '<span style="color: orange;">⚠ 不完整</span>';

                        validRows.push(`
                            <tr>
                                <td>${rowNumber}</td>
                                <td>${name || '<span style="color: #ccc;">空</span>'}</td>
                                <td>${phone || '<span style="color: #ccc;">空</span>'}</td>
                                <td>${status}</td>
                            </tr>
                        `);
                        rowNumber++;
                    }
                });

                console.log('有效行数:', validRows.length);
                tableBody.innerHTML = validRows.join('');

                // 显示统计信息
                const totalValid = validRows.filter(row => row.includes('有效')).length;
                const totalIncomplete = validRows.length - totalValid;

                const statsInfo = document.getElementById('statsInfo') || createStatsInfo();
                statsInfo.innerHTML = `
                    <div class="result-info">
                        <strong>📊 数据统计:</strong><br>
                        原始数据: ${tableData.length} 行<br>
                        有数据记录: ${validRows.length} 条<br>
                        有效联系人: ${totalValid} 个 (姓名和电话都有)<br>
                        不完整记录: ${totalIncomplete} 个 (缺少姓名或电话)
                    </div>
                `;
            }
        }

        function createStatsInfo() {
            const statsInfo = document.createElement('div');
            statsInfo.id = 'statsInfo';
            statsInfo.style.marginTop = '15px';
            document.getElementById('dataPreview').appendChild(statsInfo);
            return statsInfo;
        }

        function checkSelections() {
            const nameColumn = document.getElementById('nameColumn').value;
            const phoneColumn = document.getElementById('phoneColumn').value;
            const generateBtn = document.getElementById('generateBtn');

            // 更新预览表格
            updatePreviewTable();

            if (nameColumn !== '' && phoneColumn !== '') {
                generateBtn.disabled = false;
                document.getElementById('step3').classList.add('active');
            } else {
                generateBtn.disabled = true;
                document.getElementById('step3').classList.remove('active');
            }
        }

        function generateVCF() {
            const nameColumnIndex = parseInt(document.getElementById('nameColumn').value);
            const phoneColumnIndex = parseInt(document.getElementById('phoneColumn').value);

            if (isNaN(nameColumnIndex) || isNaN(phoneColumnIndex)) {
                showError('请选择姓名列和电话号码列');
                return;
            }

            // 显示进度条
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            progressBar.classList.remove('hidden');

            let vcfContent = '';
            let validContacts = 0;
            let skippedContacts = 0;
            let totalProcessed = 0;
            let totalRows = tableData.length;

            // 使用setTimeout来避免阻塞UI
            let currentIndex = 0;

            function processNextBatch() {
                const batchSize = 50; // 每批处理50条记录
                const endIndex = Math.min(currentIndex + batchSize, totalRows);

                for (let i = currentIndex; i < endIndex; i++) {
                    const row = tableData[i];
                    const name = String(row[nameColumnIndex] || '').trim();
                    const phone = String(row[phoneColumnIndex] || '').trim();

                    totalProcessed++;

                    if (name && phone) {
                        // 清理电话号码 - 保留数字、+号、-号、空格、括号
                        const cleanPhone = phone.replace(/[^\d+\-\s()]/g, '');

                        if (cleanPhone) {
                            vcfContent += `BEGIN:VCARD\n`;
                            vcfContent += `VERSION:3.0\n`;
                            vcfContent += `FN:${name}\n`;
                            vcfContent += `N:${name};;;;\n`;
                            vcfContent += `TEL:${cleanPhone}\n`;
                            vcfContent += `END:VCARD\n`;

                            validContacts++;
                        } else {
                            skippedContacts++;
                        }
                    } else {
                        skippedContacts++;
                    }

                    // 更新进度
                    const progress = (totalProcessed / totalRows) * 100;
                    progressFill.style.width = progress + '%';
                }

                currentIndex = endIndex;

                if (currentIndex < totalRows) {
                    // 继续处理下一批
                    setTimeout(processNextBatch, 10);
                } else {
                    // 处理完成
                    finishGeneration();
                }
            }

            function finishGeneration() {
                if (validContacts > 0) {
                    downloadVCF(vcfContent, validContacts, skippedContacts, totalProcessed);
                } else {
                    showError(`没有找到有效的联系人数据。处理了 ${totalProcessed} 条记录，跳过了 ${skippedContacts} 条无效记录。`);
                    progressBar.classList.add('hidden');
                }
            }

            // 开始处理
            processNextBatch();
        }

        function downloadVCF(content, validCount, skippedCount, totalCount) {
            const blob = new Blob([content], { type: 'text/vcard;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `contacts_${new Date().getTime()}.vcf`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            // 显示详细的成功信息
            const resultInfo = document.getElementById('resultInfo');
            resultInfo.innerHTML = `
                <div class="result-info">
                    <strong>✅ VCF文件生成成功!</strong><br><br>
                    <strong>📊 处理统计:</strong><br>
                    • 总计处理: ${totalCount} 条记录<br>
                    • 成功转换: ${validCount} 个有效联系人<br>
                    • 跳过记录: ${skippedCount} 条 (缺少姓名或电话号码)<br><br>
                    <strong>📱 使用说明:</strong><br>
                    文件已自动下载为 contacts_${new Date().getTime()}.vcf<br>
                    您可以将此文件传输到手机并导入通讯录中
                </div>
            `;
            resultInfo.classList.remove('hidden');

            // 隐藏进度条
            setTimeout(() => {
                document.getElementById('progressBar').classList.add('hidden');
            }, 1500);
        }

        function showError(message) {
            const resultInfo = document.getElementById('resultInfo');
            resultInfo.innerHTML = `<div class="error-info"><strong>❌ 错误:</strong> ${message}</div>`;
            resultInfo.classList.remove('hidden');
        }
    </script>
</body>
</html>
